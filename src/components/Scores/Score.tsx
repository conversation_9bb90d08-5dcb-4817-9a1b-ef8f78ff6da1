import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { modules, modulePositions } from './modulesData';
import ModuleStone from './ModuleStone';
import ModuleDetailModal from './ModuleDetailModal';
import { Module } from './types/GameData';

const Score: React.FC = () => {
  const navigate = useNavigate();
  const [selectedModule, setSelectedModule] = useState<Module | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const handleModuleClick = (module: Module): void => {
    setSelectedModule(module);
    setIsModalOpen(true);
  };

  const closeModal = (): void => {
    setIsModalOpen(false);
    setSelectedModule(null);
  };

  const handleBackToHome = (): void => {
    navigate('/home');
  };

  // Path coordinates creating a curved game-style road like the reference image
  const pathData = `
    M 80 500
    C 140 480, 180 460, 220 430
    C 260 400, 300 380, 320 390
    C 340 400, 320 350, 280 320
    C 240 290, 200 270, 180 290
    C 160 310, 200 250, 260 220
    C 320 190, 380 200, 420 240
    C 460 280, 500 320, 540 360
    C 580 400, 620 420, 660 430
    C 700 440, 740 430, 720 430
    C 700 430, 680 460, 660 490
    C 640 520, 620 540, 620 530
    C 620 520, 660 510, 720 500
    C 780 490, 820 480, 820 490
    C 820 500, 840 460, 880 430
    C 920 400, 960 380, 980 370
    C 1000 360, 1020 340, 1020 290
  `;

  return (
    <div className="w-full h-screen bg-gradient-to-br from-yellow-200 via-orange-200 to-amber-300 overflow-hidden">
      <div className="relative w-full h-full">
        <svg
          width="100%"
          height="100%"
          viewBox="0 0 1200 600"
          className="absolute inset-0"
          preserveAspectRatio="xMidYMid meet"
        >
          {/* Desert background pattern */}
          <defs>
            <pattern id="sandPattern" patternUnits="userSpaceOnUse" width="40" height="40">
              <circle cx="5" cy="5" r="1" fill="#F59E0B" opacity="0.1" />
              <circle cx="15" cy="15" r="0.8" fill="#EA580C" opacity="0.1" />
              <circle cx="25" cy="8" r="1.2" fill="#F97316" opacity="0.1" />
              <circle cx="35" cy="25" r="0.9" fill="#FB923C" opacity="0.1" />
            </pattern>
            
            <filter id="pathShadow">
              <feDropShadow dx="2" dy="4" stdDeviation="3" floodColor="#92400E" floodOpacity="0.3"/>
            </filter>

            <linearGradient id="pathGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#D4A574" />
              <stop offset="50%" stopColor="#E8C4A0" />
              <stop offset="100%" stopColor="#D4A574" />
            </linearGradient>
          </defs>
          
          {/* Background with sand pattern */}
          <rect width="100%" height="100%" fill="url(#sandPattern)" />
          
          {/* Winding path - main road */}
          <path
            d={pathData}
            fill="none"
            stroke="#D4A574"
            strokeWidth="50"
            strokeLinecap="round"
            strokeLinejoin="round"
            filter="url(#pathShadow)"
            opacity="0.9"
          />

          {/* Path border/highlight */}
          <path
            d={pathData}
            fill="none"
            stroke="#E8C4A0"
            strokeWidth="30"
            strokeLinecap="round"
            strokeLinejoin="round"
            opacity="0.8"
          />

          {/* Dashed center line like in the reference image */}
          <path
            d={pathData}
            fill="none"
            stroke="#FFFFFF"
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeDasharray="12,8"
            opacity="0.9"
          />
          
          
          {/* Module stones */}
          {modules.map((module: Module) => (
            <ModuleStone
              key={module.id}
              module={module}
              position={modulePositions[module.id - 1]}
              onClick={() => handleModuleClick(module)}
            />
          ))}
        </svg>

        {/* Back Button */}
        <div className="absolute top-6 left-6 z-20">
          <button
            onClick={handleBackToHome}
            className="bg-white bg-opacity-90 hover:bg-opacity-100 text-amber-900 font-bold py-2 px-4 rounded-lg shadow-lg transition-all duration-200 hover:scale-105 flex items-center space-x-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </button>
        </div>

        
        {/* Progress indicator */}
        <div className="absolute top-8 right-8 z-10 bg-white bg-opacity-90 rounded-lg p-4 shadow-lg">
          <div className="text-sm font-medium text-gray-700 mb-2">Overall Progress</div>
          <div className="flex space-x-1">
            {modules.map((module) => (
              <div
                key={module.id}
                className={`w-3 h-3 rounded-full ${
                  module.status === 'completed' ? 'bg-green-500' :
                  module.status === 'unlocked' ? 'bg-yellow-500' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
          <div className="text-xs text-gray-600 mt-1">
            {modules.filter(m => m.status === 'completed').length} / {modules.length} completed
          </div>
        </div>
      </div>
      
      {/* Modal */}
      <ModuleDetailModal
        isOpen={isModalOpen}
        module={selectedModule}
        onClose={closeModal}
      />
    </div>
  );
};

export default Score;